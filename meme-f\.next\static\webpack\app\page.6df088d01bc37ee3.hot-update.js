"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/UploadMeme.tsx":
/*!***************************************!*\
  !*** ./src/components/UploadMeme.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UploadMeme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_autoTag__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/autoTag */ \"(app-pages-browser)/./src/lib/autoTag.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clipboard,Loader2,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clipboard,Loader2,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clipboard,Loader2,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clipboard,Loader2,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clipboard,Loader2,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Clipboard,Loader2,Plus,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction UploadMeme(param) {\n    let { onUploadSuccess, initialData } = param;\n    var _formData_image;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPasteHint, setShowPasteHint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        tags: '',\n        image: null,\n        preview: null\n    });\n    // Update form data when initialData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadMeme.useEffect\": ()=>{\n            if ((initialData === null || initialData === void 0 ? void 0 : initialData.text) && !formData.tags) {\n                setFormData({\n                    \"UploadMeme.useEffect\": (prev)=>({\n                            ...prev,\n                            tags: initialData.text ? \"shared, \".concat(initialData.text.slice(0, 20)) : 'shared'\n                        })\n                }[\"UploadMeme.useEffect\"]);\n            }\n        }\n    }[\"UploadMeme.useEffect\"], [\n        initialData,\n        formData.tags\n    ]);\n    // Global paste handler\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UploadMeme.useEffect\": ()=>{\n            const handlePaste = {\n                \"UploadMeme.useEffect.handlePaste\": (e)=>{\n                    // Only handle paste if modal is open or we're not in an input field\n                    const activeElement = document.activeElement;\n                    const isInInput = activeElement instanceof HTMLInputElement || activeElement instanceof HTMLTextAreaElement || (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute('contenteditable')) === 'true';\n                    if (!isOpen && !isInInput) {\n                        var _e_clipboardData;\n                        // Show paste hint for a few seconds\n                        const clipboardItems = (_e_clipboardData = e.clipboardData) === null || _e_clipboardData === void 0 ? void 0 : _e_clipboardData.items;\n                        if (clipboardItems) {\n                            for(let i = 0; i < clipboardItems.length; i++){\n                                if (clipboardItems[i].type.startsWith('image/')) {\n                                    setShowPasteHint(true);\n                                    setTimeout({\n                                        \"UploadMeme.useEffect.handlePaste\": ()=>setShowPasteHint(false)\n                                    }[\"UploadMeme.useEffect.handlePaste\"], 3000);\n                                    break;\n                                }\n                            }\n                        }\n                        return;\n                    }\n                    if (isOpen && !isInInput) {\n                        var _e_clipboardData1;\n                        const clipboardItems = (_e_clipboardData1 = e.clipboardData) === null || _e_clipboardData1 === void 0 ? void 0 : _e_clipboardData1.items;\n                        if (clipboardItems) {\n                            for(let i = 0; i < clipboardItems.length; i++){\n                                if (clipboardItems[i].type.startsWith('image/')) {\n                                    e.preventDefault();\n                                    const blob = clipboardItems[i].getAsFile();\n                                    if (blob) {\n                                        handleFileSelect(blob);\n                                    }\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n            }[\"UploadMeme.useEffect.handlePaste\"];\n            const handleGlobalDrop = {\n                \"UploadMeme.useEffect.handleGlobalDrop\": (e)=>{\n                    var _e_dataTransfer;\n                    // Prevent default browser behavior for file drops\n                    if ((_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.types.includes('Files')) {\n                        e.preventDefault();\n                        if (!isOpen) {\n                            var _e_dataTransfer_files;\n                            // Auto-open modal if user drops a file anywhere\n                            const file = (_e_dataTransfer_files = e.dataTransfer.files) === null || _e_dataTransfer_files === void 0 ? void 0 : _e_dataTransfer_files[0];\n                            if (file && file.type.startsWith('image/')) {\n                                setIsOpen(true);\n                                setTimeout({\n                                    \"UploadMeme.useEffect.handleGlobalDrop\": ()=>handleFileSelect(file)\n                                }[\"UploadMeme.useEffect.handleGlobalDrop\"], 100);\n                            }\n                        }\n                    }\n                }\n            }[\"UploadMeme.useEffect.handleGlobalDrop\"];\n            const handleGlobalDragOver = {\n                \"UploadMeme.useEffect.handleGlobalDragOver\": (e)=>{\n                    var _e_dataTransfer;\n                    if ((_e_dataTransfer = e.dataTransfer) === null || _e_dataTransfer === void 0 ? void 0 : _e_dataTransfer.types.includes('Files')) {\n                        e.preventDefault();\n                    }\n                }\n            }[\"UploadMeme.useEffect.handleGlobalDragOver\"];\n            document.addEventListener('paste', handlePaste);\n            document.addEventListener('drop', handleGlobalDrop);\n            document.addEventListener('dragover', handleGlobalDragOver);\n            return ({\n                \"UploadMeme.useEffect\": ()=>{\n                    document.removeEventListener('paste', handlePaste);\n                    document.removeEventListener('drop', handleGlobalDrop);\n                    document.removeEventListener('dragover', handleGlobalDragOver);\n                }\n            })[\"UploadMeme.useEffect\"];\n        }\n    }[\"UploadMeme.useEffect\"], [\n        isOpen\n    ]);\n    // Show paste hint notification\n    const PasteHint = ()=>showPasteHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: 16\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm\",\n                        children: \"Image copied! Open upload to paste it\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(null);\n        if (!formData.image) {\n            setError('Please select an image');\n            return;\n        }\n        setLoading(true);\n        try {\n            // Auto-generate tags if none provided\n            let finalTags = formData.tags.trim();\n            if (!finalTags) {\n                try {\n                    const result = await (0,_lib_autoTag__WEBPACK_IMPORTED_MODULE_4__.analyzeImageForTags)(formData.image);\n                    finalTags = result.tags.join(', ') || 'funny, meme';\n                } catch (e) {\n                    finalTags = 'funny, meme';\n                }\n            }\n            const uploadData = {\n                tags: finalTags,\n                image: formData.image\n            };\n            const response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_3__.uploadMeme)(uploadData);\n            if (response.success) {\n                setFormData({\n                    tags: '',\n                    image: null,\n                    preview: null\n                });\n                setIsOpen(false);\n                onUploadSuccess === null || onUploadSuccess === void 0 ? void 0 : onUploadSuccess();\n            } else {\n                setError(response.message || 'Upload failed');\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Upload failed');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleFileSelect = (file)=>{\n        if (!file.type.startsWith('image/')) {\n            setError('Please select an image file');\n            return;\n        }\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        // Create preview\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            setFormData((prev)=>{\n                var _e_target;\n                return {\n                    ...prev,\n                    image: file,\n                    preview: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result\n                };\n            });\n        };\n        reader.readAsDataURL(file);\n        setError(null);\n    };\n    const handleFileChange = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) handleFileSelect(file);\n    };\n    const handleDrag = (e)=>{\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(e.type === \"dragenter\" || e.type === \"dragover\");\n    };\n    const handleDrop = (e)=>{\n        var _e_dataTransfer_files;\n        e.preventDefault();\n        e.stopPropagation();\n        setDragActive(false);\n        const file = (_e_dataTransfer_files = e.dataTransfer.files) === null || _e_dataTransfer_files === void 0 ? void 0 : _e_dataTransfer_files[0];\n        if (file) handleFileSelect(file);\n    };\n    const resetForm = ()=>{\n        setFormData({\n            title: '',\n            tags: '',\n            image: null,\n            preview: null\n        });\n        setError(null);\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PasteHint, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsOpen(true),\n                    className: \"fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 group\",\n                    title: \"Upload a meme (or paste/drop anywhere)\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 24,\n                        className: \"group-hover:rotate-90 transition-transform duration-200\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PasteHint, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl w-full max-w-lg shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Upload Meme\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsOpen(false),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed rounded-lg p-8 text-center transition-all \".concat(dragActive ? 'border-blue-400 bg-blue-50' : formData.image ? 'border-green-400 bg-green-50' : 'border-gray-300 hover:border-gray-400'),\n                                                onDragEnter: handleDrag,\n                                                onDragLeave: handleDrag,\n                                                onDragOver: handleDrag,\n                                                onDrop: handleDrop,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        onChange: handleFileChange,\n                                                        className: \"hidden\",\n                                                        id: \"file-upload\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    formData.preview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative w-32 h-32 mx-auto\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: formData.preview,\n                                                                    alt: \"Preview\",\n                                                                    fill: true,\n                                                                    className: \"rounded-lg object-cover\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-green-700 font-medium\",\n                                                                children: (_formData_image = formData.image) === null || _formData_image === void 0 ? void 0 : _formData_image.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2 justify-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        htmlFor: \"file-upload\",\n                                                                        className: \"px-4 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\",\n                                                                        children: \"Change\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: resetForm,\n                                                                        className: \"px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors\",\n                                                                        children: \"Remove\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                        lineNumber: 304,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                size: 40,\n                                                                className: \"mx-auto text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-lg font-medium text-gray-700 mb-1\",\n                                                                        children: \"Drop your meme here\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                        lineNumber: 317,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500 mb-2\",\n                                                                        children: \"or click to browse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center gap-4 text-xs text-gray-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                        size: 12\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                                        lineNumber: 323,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Drag & Drop\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                                        lineNumber: 324,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                                lineNumber: 322,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-px h-4 bg-gray-300\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                                lineNumber: 326,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                        size: 12\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                                        lineNumber: 328,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Ctrl+V to Paste\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                                        lineNumber: 329,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"file-upload\",\n                                                                className: \"inline-block px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer transition-colors font-medium\",\n                                                                children: \"Choose File\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: \"PNG, JPG, GIF up to 10MB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 13\n                                        }, this),\n                                        formData.image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Tags (optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.tags,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    tags: e.target.value\n                                                                })),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors\",\n                                                        placeholder: \"funny, relatable, viral...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1\",\n                                                        children: \"Separate with commas. Leave empty for auto-generated tags.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        formData.image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setIsOpen(false),\n                                                    className: \"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\",\n                                                    disabled: loading,\n                                                    children: \"Cancel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: loading,\n                                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium flex items-center justify-center gap-2\",\n                                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                size: 16,\n                                                                className: \"animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Uploading...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Clipboard_Loader2_Plus_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Upload Meme\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\UploadMeme.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(UploadMeme, \"cWLGG9QrVHddz/xobipiQjKKZTQ=\");\n_c = UploadMeme;\nvar _c;\n$RefreshReg$(_c, \"UploadMeme\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UploadMeme.tsx\n"));

/***/ })

});