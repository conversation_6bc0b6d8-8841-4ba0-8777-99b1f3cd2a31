"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _components_MemeCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/MemeCard */ \"(app-pages-browser)/./src/components/MemeCard.tsx\");\n/* harmony import */ var _components_UploadMeme__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/UploadMeme */ \"(app-pages-browser)/./src/components/UploadMeme.tsx\");\n/* harmony import */ var _components_SearchFilters__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/SearchFilters */ \"(app-pages-browser)/./src/components/SearchFilters.tsx\");\n/* harmony import */ var _components_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LoadingSkeleton */ \"(app-pages-browser)/./src/components/LoadingSkeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [memes, setMemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [backendStatus, setBackendStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('checking');\n    const [currentQuery, setCurrentQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check backend health\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const checkBackend = {\n                \"Home.useEffect.checkBackend\": async ()=>{\n                    try {\n                        await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.healthCheck)();\n                        setBackendStatus('online');\n                    } catch (e) {\n                        setBackendStatus('offline');\n                    }\n                }\n            }[\"Home.useEffect.checkBackend\"];\n            checkBackend();\n        }\n    }[\"Home.useEffect\"], []);\n    // Load memes\n    const loadMemes = async (query)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = query && (query.q || query.tag || query.title || query.limit) ? await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.searchMemes)(query) : await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.getMemes)();\n            if (response.success && response.data) {\n                setMemes(response.data);\n                setCurrentQuery(query || null);\n            } else {\n                setError(response.message || 'Failed to load memes');\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load memes');\n            setBackendStatus('offline');\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            loadMemes();\n        }\n    }[\"Home.useEffect\"], []);\n    // Handle search\n    const handleSearch = (query)=>{\n        loadMemes(query);\n    };\n    // Handle clear search\n    const handleClearSearch = ()=>{\n        setCurrentQuery(null);\n        loadMemes();\n    };\n    // Handle delete\n    const handleDelete = async (id)=>{\n        if (!confirm('Are you sure you want to delete this meme?')) {\n            return;\n        }\n        try {\n            const response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.deleteMeme)(id);\n            if (response.success) {\n                // Reload memes after deletion\n                loadMemes(currentQuery || undefined);\n            } else {\n                alert('Failed to delete meme: ' + response.message);\n            }\n        } catch (err) {\n            alert('Failed to delete meme: ' + (err instanceof Error ? err.message : 'Unknown error'));\n        }\n    };\n    // Handle upload success\n    const handleUploadSuccess = ()=>{\n        loadMemes(currentQuery || undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 sticky top-0 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: \"MemeDB\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Public meme collection\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/save-memes.html\",\n                                            target: \"_blank\",\n                                            className: \"hidden sm:block px-4 py-2 text-sm bg-gradient-to-r from-purple-500 to-blue-600 text-white hover:from-purple-600 hover:to-blue-700 rounded-lg transition-all font-medium shadow-md hover:shadow-lg\",\n                                            title: \"Get the new Drag-to-Save Bookmarklet!\",\n                                            children: \"\\uD83C\\uDFAF Drag-to-Save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/help\",\n                                            className: \"hidden md:block px-3 py-1.5 text-xs bg-green-50 text-green-700 hover:bg-green-100 rounded-lg transition-colors font-medium\",\n                                            title: \"Learn how to save memes from anywhere\",\n                                            children: \"❓ Help\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block\",\n                                            children: [\n                                                backendStatus === 'online' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1.5 text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Online\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                backendStatus === 'offline' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1.5 text-red-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Offline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                backendStatus === 'checking' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1.5 text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Connecting\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchFilters__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            onSearch: handleSearch,\n                                            onClear: handleClearSearch\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>loadMemes(currentQuery || undefined),\n                                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors\",\n                                            disabled: loading,\n                                            title: \"Refresh\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 18,\n                                                className: loading ? 'animate-spin' : ''\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        currentQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Showing results for: \"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this),\n                                currentQuery.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"“\",\n                                        currentQuery.title,\n                                        \"”\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 38\n                                }, this),\n                                currentQuery.tag && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"#\",\n                                        currentQuery.tag\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 36\n                                }, this),\n                                currentQuery.q && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"“\",\n                                        currentQuery.q,\n                                        \"”\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleClearSearch,\n                                    className: \"ml-2 text-blue-600 hover:text-blue-800 underline\",\n                                    children: \"Clear\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-6xl mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-lg\",\n                                                children: \"\\uD83C\\uDFAF\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-gray-900\",\n                                                    children: \"New: Drag-to-Save Bookmarklet!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Save memes from any website by dragging images to a floating drop zone\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/save-memes.html\",\n                                    target: \"_blank\",\n                                    className: \"px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-600 text-white rounded-lg hover:from-purple-600 hover:to-blue-700 transition-all font-medium text-sm\",\n                                    children: \"Get It Now →\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium\",\n                                            children: \"Error\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingSkeleton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 21\n                    }, this),\n                    !loading && !error && memes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-gray-400\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: currentQuery ? 'No memes found' : 'No memes yet'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: currentQuery ? 'Try adjusting your search or browse all memes' : 'Be the first to upload a meme to the collection!'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            currentQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClearSearch,\n                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: \"Show All Memes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    !loading && !error && memes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    memes.length,\n                                    \" meme\",\n                                    memes.length !== 1 ? 's' : '',\n                                    \" found\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4\",\n                                children: memes.map((meme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MemeCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        meme: meme,\n                                        onDelete: handleDelete\n                                    }, meme.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UploadMeme__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onUploadSuccess: handleUploadSuccess\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"t3usFBfXM6iWQy3AQP128A7MlTo=\");\n_c = Home;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});