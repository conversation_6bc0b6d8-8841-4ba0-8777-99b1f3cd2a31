'use client';

import { useState } from 'react';
import { Meme } from '@/types/meme';
import { formatDate, getImageUrl } from '@/lib/utils';
import { Trash2, Eye } from 'lucide-react';
import Image from 'next/image';
import FullScreenMeme from './FullScreenMeme';

interface MemeCardProps {
  meme: Meme;
  onDelete?: (id: string) => void;
}

export default function MemeCard({ meme, onDelete }: MemeCardProps) {
  const [isFullScreenOpen, setIsFullScreenOpen] = useState(false);

  const handleDelete = () => {
    if (onDelete) {
      onDelete(meme.id);
    }
  };

  const handleImageClick = () => {
    setIsFullScreenOpen(true);
  };

  const handleCloseFullScreen = () => {
    setIsFullScreenOpen(false);
  };

  return (
    <>
      <div className="group relative bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden border border-gray-100">
        {/* Image Container */}
        <div 
          className="relative aspect-square overflow-hidden cursor-pointer bg-gray-50"
          onClick={handleImageClick}
        >
          <Image
            src={getImageUrl(meme.image_url)}
            alt={meme.title || 'Meme'}
            fill
            className="object-cover group-hover:scale-[1.02] transition-transform duration-200"
            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
            onError={(e) => {
              e.currentTarget.src = '/placeholder-meme.svg';
            }}
          />
          
          {/* Hover Overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200" />
          
          {/* Action Buttons */}
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <button
              onClick={handleImageClick}
              className="bg-white/95 hover:bg-white text-gray-700 hover:text-gray-900 p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110"
              title="View full size"
            >
              <Eye size={18} />
            </button>
          </div>
          
          {/* Delete Button */}
          {onDelete && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDelete();
              }}
              className="absolute top-2 right-2 bg-red-500/90 hover:bg-red-600 text-white p-1.5 rounded-full transition-all duration-200 opacity-0 group-hover:opacity-100 shadow-md hover:scale-110"
              title="Delete meme"
            >
              <Trash2 size={14} />
            </button>
          )}
        </div>

        {/* Content - Only show if title exists or has meaningful info */}
        {(meme.title || meme.tags.length > 0) && (
          <div className="p-3">
            {/* Title */}
            {meme.title && (
              <h3 className="text-sm font-medium text-gray-900 mb-2 line-clamp-2">
                {meme.title}
              </h3>
            )}

            {/* Tags - Show max 2 tags, filter out system tags */}
            {(() => {
              const systemTags = ['bookmarklet', 'drag-saved', 'browser-extension', 'saved'];
              const visibleTags = meme.tags.filter(tag => !systemTags.includes(tag.toLowerCase()));
              return visibleTags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-2">
                  {visibleTags.slice(0, 2).map((tag, index) => (
                    <span key={index} className="inline-block px-2 py-0.5 bg-blue-50 text-blue-600 text-xs rounded-md font-medium">
                      #{tag}
                    </span>
                  ))}
                  {visibleTags.length > 2 && (
                    <span className="inline-block px-2 py-0.5 bg-gray-50 text-gray-500 text-xs rounded-md">
                      +{visibleTags.length - 2}
                    </span>
                  )}
                </div>
              );
            })()}

            {/* Date - Simplified */}
            <div className="text-xs text-gray-400">
              {formatDate(meme.created_at)}
            </div>
          </div>
        )}
      </div>

      {/* Full Screen Modal */}
      <FullScreenMeme 
        meme={meme}
        isOpen={isFullScreenOpen}
        onClose={handleCloseFullScreen}
      />
    </>
  );
}
