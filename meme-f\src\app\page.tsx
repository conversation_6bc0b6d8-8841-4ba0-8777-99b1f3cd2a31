'use client';

import { useState, useEffect } from 'react';
import { Meme, SearchQuery } from '@/types/meme';
import { getMemes, searchMemes, deleteMeme, healthCheck } from '@/lib/api';
import MemeCard from '@/components/MemeCard';
import UploadMeme from '@/components/UploadMeme';
import SearchFilters from '@/components/SearchFilters';
import LoadingSkeleton from '@/components/LoadingSkeleton';
import { RefreshCw, AlertCircle, CheckCircle } from 'lucide-react';

function Home() {
  const [memes, setMemes] = useState<Meme[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const [currentQuery, setCurrentQuery] = useState<SearchQuery | null>(null);

  // Check backend health
  useEffect(() => {
    const checkBackend = async () => {
      try {
        await healthCheck();
        setBackendStatus('online');
      } catch {
        setBackendStatus('offline');
      }
    };
    checkBackend();
  }, []);

  // Load memes
  const loadMemes = async (query?: SearchQuery) => {
    setLoading(true);
    setError(null);

    try {
      const response = query && (query.q || query.tag || query.limit)
        ? await searchMemes(query)
        : await getMemes();

      if (response.success && response.data) {
        setMemes(response.data);
        setCurrentQuery(query || null);
      } else {
        setError(response.message || 'Failed to load memes');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load memes');
      setBackendStatus('offline');
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadMemes();
  }, []);

  // Handle search
  const handleSearch = (query: SearchQuery) => {
    loadMemes(query);
  };

  // Handle clear search
  const handleClearSearch = () => {
    setCurrentQuery(null);
    loadMemes();
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this meme?')) {
      return;
    }

    try {
      const response = await deleteMeme(id);
      if (response.success) {
        // Reload memes after deletion
        loadMemes(currentQuery || undefined);
      } else {
        alert('Failed to delete meme: ' + response.message);
      }
    } catch (err) {
      alert('Failed to delete meme: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  // Handle upload success
  const handleUploadSuccess = () => {
    loadMemes(currentQuery || undefined);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Simplified Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-4">
          {/* Simple Header Layout */}
          <div className="flex items-center justify-between">
            {/* Logo & Title */}
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                MemeDB
              </h1>
              <p className="text-sm text-gray-600">Public meme collection</p>
            </div>

            {/* Header Actions */}
            <div className="flex items-center gap-3">
              {/* Bookmarklet Link */}
              <a
                href="/save-memes.html"
                target="_blank"
                className="hidden sm:block px-4 py-2 text-sm bg-gradient-to-r from-purple-500 to-blue-600 text-white hover:from-purple-600 hover:to-blue-700 rounded-lg transition-all font-medium shadow-md hover:shadow-lg"
                title="Get the new Drag-to-Save Bookmarklet!"
              >
                🎯 Drag-to-Save
              </a>
              <a
                href="/help"
                className="hidden md:block px-3 py-1.5 text-xs bg-green-50 text-green-700 hover:bg-green-100 rounded-lg transition-colors font-medium"
                title="Learn how to save memes from anywhere"
              >
                ❓ Help
              </a>

              {/* Status Indicator */}
              <div className="hidden sm:block">
                {backendStatus === 'online' && (
                  <div className="flex items-center gap-1.5 text-green-600">
                    <CheckCircle size={16} />
                    <span className="text-sm font-medium">Online</span>
                  </div>
                )}
                {backendStatus === 'offline' && (
                  <div className="flex items-center gap-1.5 text-red-600">
                    <AlertCircle size={16} />
                    <span className="text-sm font-medium">Offline</span>
                  </div>
                )}
                {backendStatus === 'checking' && (
                  <div className="flex items-center gap-1.5 text-gray-600">
                    <RefreshCw size={16} className="animate-spin" />
                    <span className="text-sm font-medium">Connecting</span>
                  </div>
                )}
              </div>

              {/* Search & Refresh */}
              <SearchFilters onSearch={handleSearch} onClear={handleClearSearch} />
              <button
                onClick={() => loadMemes(currentQuery || undefined)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                disabled={loading}
                title="Refresh"
              >
                <RefreshCw size={18} className={loading ? 'animate-spin' : ''} />
              </button>
            </div>
          </div>

          {/* Search Results Info */}
          {currentQuery && (
            <div className="mt-3 text-sm text-gray-600">
              <span>Showing results for: </span>
              {currentQuery.tag && <span className="font-medium">#{currentQuery.tag}</span>}
              {currentQuery.q && <span className="font-medium">&ldquo;{currentQuery.q}&rdquo;</span>}
              <button
                onClick={handleClearSearch}
                className="ml-2 text-blue-600 hover:text-blue-800 underline"
              >
                Clear
              </button>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 py-8">
        {/* New Feature Banner */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white text-lg">🎯</span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">New: Drag-to-Save Bookmarklet!</h3>
                <p className="text-sm text-gray-600">Save memes from any website by dragging images to a floating drop zone</p>
              </div>
            </div>
            <a
              href="/save-memes.html"
              target="_blank"
              className="px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-600 text-white rounded-lg hover:from-purple-600 hover:to-blue-700 transition-all font-medium text-sm"
            >
              Get It Now →
            </a>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle size={20} />
              <div>
                <h3 className="font-medium">Error</h3>
                <p className="text-sm">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && <LoadingSkeleton />}

        {/* Empty State */}
        {!loading && !error && memes.length === 0 && (
          <div className="text-center py-16">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {currentQuery ? 'No memes found' : 'No memes yet'}
            </h3>
            <p className="text-gray-600 mb-4">
              {currentQuery 
                ? 'Try adjusting your search or browse all memes' 
                : 'Be the first to upload a meme to the collection!'
              }
            </p>
            {currentQuery && (
              <button
                onClick={handleClearSearch}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Show All Memes
              </button>
            )}
          </div>
        )}

        {/* Memes Grid */}
        {!loading && !error && memes.length > 0 && (
          <div className="space-y-6">
            {/* Results Count */}
            <div className="text-sm text-gray-600">
              {memes.length} meme{memes.length !== 1 ? 's' : ''} found
            </div>

            {/* Grid */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {memes.map(meme => (
                <MemeCard
                  key={meme.id}
                  meme={meme}
                  onDelete={handleDelete}
                />
              ))}
            </div>
          </div>
        )}
      </main>

      {/* Upload FAB */}
      <UploadMeme onUploadSuccess={handleUploadSuccess} />
    </div>
  );
}

export default Home;
