{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate, max-age=0"}], "regex": "^(?:\\/(.*))(?:\\/)?$"}, {"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate, max-age=0"}, {"key": "Service-Worker-Allowed", "value": "/"}], "regex": "^\\/sw\\.js(?:\\/)?$"}]}