/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze-meme/route";
exports.ids = ["app/api/analyze-meme/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze-meme%2Froute&page=%2Fapi%2Fanalyze-meme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-meme%2Froute.ts&appDir=C%3A%5CUsers%5Ctemi%5CDesktop%5Cmemedb%5Cmeme-f%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ctemi%5CDesktop%5Cmemedb%5Cmeme-f&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze-meme%2Froute&page=%2Fapi%2Fanalyze-meme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-meme%2Froute.ts&appDir=C%3A%5CUsers%5Ctemi%5CDesktop%5Cmemedb%5Cmeme-f%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ctemi%5CDesktop%5Cmemedb%5Cmeme-f&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_temi_Desktop_memedb_meme_f_src_app_api_analyze_meme_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/analyze-meme/route.ts */ \"(rsc)/./src/app/api/analyze-meme/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze-meme/route\",\n        pathname: \"/api/analyze-meme\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze-meme/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\app\\\\api\\\\analyze-meme\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_temi_Desktop_memedb_meme_f_src_app_api_analyze_meme_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/analyze-meme/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze-meme%2Froute&page=%2Fapi%2Fanalyze-meme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-meme%2Froute.ts&appDir=C%3A%5CUsers%5Ctemi%5CDesktop%5Cmemedb%5Cmeme-f%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ctemi%5CDesktop%5Cmemedb%5Cmeme-f&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analyze-meme/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/analyze-meme/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Google Gemini Vision AI analysis - Actually analyzes image content\nasync function analyzeImageWithGemini(imageBase64) {\n    try {\n        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' + process.env.GEMINI_API_KEY, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                contents: [\n                    {\n                        parts: [\n                            {\n                                text: `Analyze this image and generate 2-3 highly relevant, searchable tags. Focus on the MOST important aspects:\n\n1. Visual content: What do you see in the image?\n2. Text in image: Read any text overlays or captions\n3. Format: Recognize popular templates (Drake, Distracted Boyfriend, etc.)\n4. Facial expressions: Emotions, reactions shown\n5. Context clues: Setting, objects, characters\n\nGenerate only the BEST 2-3 tags that people would actually search for to find this content. Focus on:\n- Most recognizable format or template name (if applicable)\n- Primary emotion/reaction\n- Main topic/theme\n- Specific characters or objects\n\nAVOID generic words like \"meme\", \"image\", \"funny\" unless they are truly the most relevant descriptor.\n\nRespond with JSON only:\n{\n  \"tags\": [\"tag1\", \"tag2\", \"tag3\"],\n  \"confidence\": 0.95,\n  \"category\": \"reaction-content\",\n  \"description\": \"Brief description of what you see\"\n}\n\nIgnore the title if provided - analyze purely based on visual content.`\n                            },\n                            {\n                                inline_data: {\n                                    mime_type: \"image/jpeg\",\n                                    data: imageBase64\n                                }\n                            }\n                        ]\n                    }\n                ],\n                generationConfig: {\n                    temperature: 0.1,\n                    topK: 32,\n                    topP: 1,\n                    maxOutputTokens: 200\n                }\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`Gemini API request failed: ${response.status}`);\n        }\n        const data = await response.json();\n        const content = data.candidates[0].content.parts[0].text;\n        try {\n            // Clean up the response to extract JSON\n            const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n            if (jsonMatch) {\n                const parsed = JSON.parse(jsonMatch[0]);\n                return {\n                    tags: parsed.tags || [\n                        'funny',\n                        'relatable'\n                    ],\n                    confidence: parsed.confidence || 0.8,\n                    category: parsed.category || 'ai_analyzed',\n                    description: parsed.description\n                };\n            } else {\n                throw new Error('No JSON found in response');\n            }\n        } catch  {\n            console.error('Failed to parse Gemini response:', content);\n            throw new Error('Invalid response format from Gemini');\n        }\n    } catch (error) {\n        console.error('Gemini analysis failed:', error);\n        throw error;\n    }\n}\n// Uncomment and modify this for real OpenAI integration:\n/*\r\nasync function analyzeImageWithOpenAI(imageBase64: string, title?: string): Promise<AnalyzeMemeResponse> {\r\n  const response = await fetch('https://api.openai.com/v1/chat/completions', {\r\n    method: 'POST',\r\n    headers: {\r\n      'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,\r\n      'Content-Type': 'application/json',\r\n    },\r\n    body: JSON.stringify({\r\n      model: 'gpt-4-vision-preview',\r\n      messages: [\r\n        {\r\n          role: 'user',\r\n          content: [\r\n            {\r\n              type: 'text',\r\n              text: `Analyze this meme image and provide relevant tags. Consider the visual content, any text in the image, meme format recognition, and cultural context. Title: \"${title || 'No title provided'}\". \r\n\r\nPlease respond with a JSON object containing:\r\n- tags: array of 4-6 relevant tags (prioritize meme formats, emotions, topics, cultural references)\r\n- confidence: number between 0-1 indicating analysis confidence\r\n- category: main category (reaction, choice, wholesome, work, gaming, etc.)\r\n- description: brief description of the meme content\r\n\r\nFocus on tags that would help people find this meme when searching.`\r\n            },\r\n            {\r\n              type: 'image_url',\r\n              image_url: {\r\n                url: `data:image/jpeg;base64,${imageBase64}`\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ],\r\n      max_tokens: 300\r\n    })\r\n  });\r\n\r\n  const data = await response.json();\r\n  const content = data.choices[0].message.content;\r\n  \r\n  try {\r\n    return JSON.parse(content);\r\n  } catch (error) {\r\n    throw new Error('Failed to parse AI response');\r\n  }\r\n}\r\n*/ // Enhanced pattern analysis fallback with better accuracy\nasync function enhancedPatternAnalysis(imageBase64, title) {\n    const tags = new Set();\n    let category = 'general';\n    let confidence = 0.6;\n    let description = 'Meme analyzed using pattern recognition';\n    if (title) {\n        const titleLower = title.toLowerCase();\n        // Specific meme format detection with high accuracy\n        if ([\n            'drake',\n            'pointing'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('drake');\n            tags.add('choice');\n            category = 'choice-meme';\n            confidence = 0.9;\n            description = 'Drake pointing meme showing preference between options';\n        } else if ([\n            'surprised',\n            'pikachu',\n            'shocked'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('surprised-pikachu');\n            tags.add('reaction');\n            category = 'reaction-meme';\n            confidence = 0.85;\n            description = 'Surprised Pikachu reaction meme expressing shock or disbelief';\n        } else if ([\n            'distracted',\n            'boyfriend',\n            'looking'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('distracted-boyfriend');\n            tags.add('choice');\n            category = 'choice-meme';\n            confidence = 0.85;\n            description = 'Distracted boyfriend meme about making choices or being tempted';\n        } else if ([\n            'this is fine',\n            'fire',\n            'burning',\n            'chaos'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('this-is-fine');\n            tags.add('stress');\n            category = 'stress-meme';\n            confidence = 0.85;\n            description = 'This is fine meme about staying calm during chaos';\n        } else if ([\n            'brain',\n            'expanding',\n            'galaxy',\n            'smart'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('expanding-brain');\n            tags.add('smart');\n            category = 'intelligence-meme';\n            confidence = 0.8;\n            description = 'Expanding brain meme showing levels of intelligence or enlightenment';\n        } else if ([\n            'monday',\n            'work',\n            'office',\n            'job',\n            'boss'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('work');\n            tags.add('relatable');\n            category = 'work-meme';\n            confidence = 0.75;\n            description = 'Work-related meme about office life and professional struggles';\n        } else if ([\n            'weekend',\n            'friday',\n            'party',\n            'fun'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('weekend');\n            tags.add('fun');\n            category = 'weekend-meme';\n            confidence = 0.75;\n            description = 'Weekend/Friday meme about enjoying free time';\n        } else if ([\n            'game',\n            'gaming',\n            'gamer',\n            'noob',\n            'pro'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('gaming');\n            tags.add('relatable');\n            category = 'gaming-meme';\n            confidence = 0.75;\n            description = 'Gaming-related meme about video games and gamer culture';\n        } else if ([\n            'cute',\n            'wholesome',\n            'doggo',\n            'pupper',\n            'sweet'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('wholesome');\n            tags.add('cute');\n            category = 'wholesome-meme';\n            confidence = 0.7;\n            description = 'Wholesome meme with positive, heartwarming content';\n        } else if ([\n            'reaction',\n            'when',\n            'face',\n            'mood'\n        ].some((word)=>titleLower.includes(word))) {\n            tags.add('reaction');\n            tags.add('relatable');\n            category = 'reaction-meme';\n            confidence = 0.7;\n            description = 'Reaction meme expressing emotions or relatable situations';\n        }\n        // Extract only one key emotional/descriptive word\n        const emotionalWords = [\n            'funny',\n            'hilarious',\n            'relatable',\n            'mood',\n            'blessed',\n            'cursed'\n        ];\n        for (const word of emotionalWords){\n            if (titleLower.includes(word) && tags.size < 3) {\n                tags.add(word);\n                break; // Only add one emotional word\n            }\n        }\n        // Add only one relevant word from title if we still need tags\n        if (tags.size < 3) {\n            const words = titleLower.split(/\\s+/).filter((word)=>word.length > 2 && ![\n                    'the',\n                    'and',\n                    'for',\n                    'with',\n                    'when',\n                    'this',\n                    'that',\n                    'you',\n                    'are',\n                    'but',\n                    'not',\n                    'can',\n                    'was',\n                    'has'\n                ].includes(word));\n            if (words.length > 0) {\n                tags.add(words[0]); // Only add the first relevant word\n            }\n        }\n    }\n    // Ensure we have good default tags but limit to 3\n    if (tags.size === 0) {\n        tags.add('funny');\n        tags.add('relatable');\n        description = 'General humorous content';\n    } else if (tags.size === 1) {\n        tags.add('funny');\n    }\n    return {\n        tags: Array.from(tags).slice(0, 3),\n        confidence,\n        category,\n        description\n    };\n}\nasync function POST(request) {\n    try {\n        const { image, title, mimeType } = await request.json();\n        if (!image) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No image provided'\n            }, {\n                status: 400\n            });\n        }\n        // Validate image type\n        if (!mimeType || !mimeType.startsWith('image/')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid image format'\n            }, {\n                status: 400\n            });\n        }\n        try {\n            // Use Gemini Vision AI for true image analysis\n            const analysis = await analyzeImageWithGemini(image);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(analysis);\n        } catch (geminiError) {\n            console.warn('Gemini analysis failed, using fallback:', geminiError);\n            // Fallback to enhanced pattern matching\n            const analysis = await enhancedPatternAnalysis(image, title);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(analysis);\n        }\n    } catch (error) {\n        console.error('Meme analysis error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to analyze meme'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze-meme/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze-meme%2Froute&page=%2Fapi%2Fanalyze-meme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze-meme%2Froute.ts&appDir=C%3A%5CUsers%5Ctemi%5CDesktop%5Cmemedb%5Cmeme-f%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ctemi%5CDesktop%5Cmemedb%5Cmeme-f&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();