exports.id=831,exports.ids=[831],exports.modules={440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},1135:()=>{},1620:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},2185:(a,b,c)=>{"use strict";c.d(b,{UF:()=>h,_I:()=>g,bZ:()=>f,c2:()=>j,c4:()=>i});let d="http://127.0.0.1:3001";async function e(a,b){try{let c=await fetch(`${d}${a}`,{headers:{"Content-Type":"application/json",...b?.headers},...b});if(!c.ok)throw Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(a){throw console.error("API call failed:",a),a}}async function f(){return e("/")}async function g(){return e("/api/memes")}async function h(a){let b=new FormData;b.append("tags",a.tags),b.append("image",a.image);try{let a=await fetch(`${d}/api/memes/upload`,{method:"POST",body:b});if(!a.ok)throw Error(`HTTP error! status: ${a.status}`);return await a.json()}catch(a){throw console.error("Upload failed:",a),a}}async function i(a){return e(`/api/memes/${a}`,{method:"DELETE"})}async function j(a){let b=new URLSearchParams;return a.q&&b.append("q",a.q),a.tag&&b.append("tag",a.tag),a.limit&&b.append("limit",a.limit.toString()),e(`/api/memes/search?${b.toString()}`)}},2255:(a,b,c)=>{"use strict";c.d(b,{A:()=>s});var d=c(687),e=c(3210),f=c(474),g=c(2185);async function h(a){return new Promise((b,c)=>{let d=new FileReader;d.onload=()=>{b(d.result.split(",")[1])},d.onerror=c,d.readAsDataURL(a)})}async function i(a){try{let b=await h(a),c=await fetch("/api/analyze-meme",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({image:b,mimeType:a.type})});if(!c.ok)throw Error(`API request failed: ${c.status}`);let d=await c.json();return{tags:d.tags||[],confidence:d.confidence||.9,category:d.category,description:d.description}}catch(a){throw console.error("AI analysis failed:",a),a}}let j={filenamePatterns:[{pattern:/funny/i,tags:["funny"]},{pattern:/cat/i,tags:["cat"]},{pattern:/dog/i,tags:["dog"]},{pattern:/reaction/i,tags:["reaction"]},{pattern:/drake/i,tags:["drake"]},{pattern:/stonks/i,tags:["stonks"]},{pattern:/pikachu/i,tags:["pikachu","surprised"]},{pattern:/wojak/i,tags:["wojak"]},{pattern:/pepe/i,tags:["pepe"]},{pattern:/chad/i,tags:["chad"]}]};async function k(a){try{return await i(a)}catch(b){return console.warn("AI analysis failed, using fallback:",b),function(a){let b=new Set,c=.3,d=a.name.toLowerCase();j.filenamePatterns.forEach(({pattern:a,tags:e})=>{a.test(d)&&(e.forEach(a=>b.add(a)),c+=.2)});let e=new Date().getFullYear();return d.includes(e.toString())&&(b.add("current-year"),c+=.1),0===b.size&&(b.add("funny"),b.add("relatable"),c=.5),{tags:Array.from(b).slice(0,3),confidence:Math.min(c,1),category:"pattern_matched"}}(a)}}var l=c(2688);let m=(0,l.A)("clipboard",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}]]),n=(0,l.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var o=c(1860),p=c(3613),q=c(6023);let r=(0,l.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);function s({onUploadSuccess:a,initialData:b}){let[c,h]=(0,e.useState)(!1),[i,j]=(0,e.useState)(!1),[l,s]=(0,e.useState)(null),[t,u]=(0,e.useState)(!1),[v,w]=(0,e.useState)(!1),[x,y]=(0,e.useState)({tags:"",image:null,preview:null}),z=()=>v&&(0,d.jsx)("div",{className:"fixed top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(m,{size:16}),(0,d.jsx)("span",{className:"text-sm",children:"Image copied! Open upload to paste it"})]})}),A=async b=>{if(b.preventDefault(),s(null),!x.image)return void s("Please select an image");j(!0);try{let b=x.tags.trim();if(!b)try{b=(await k(x.image)).tags.join(", ")||"funny, meme"}catch{b="funny, meme"}let c={tags:b,image:x.image},d=await (0,g.UF)(c);d.success?(y({tags:"",image:null,preview:null}),h(!1),a?.()):s(d.message||"Upload failed")}catch(a){s(a instanceof Error?a.message:"Upload failed")}finally{j(!1)}},B=a=>{if(!a.type.startsWith("image/"))return void s("Please select an image file");if(a.size>0xa00000)return void s("File size must be less than 10MB");let b=new FileReader;b.onload=b=>{y(c=>({...c,image:a,preview:b.target?.result,title:c.title||a.name.split(".")[0].replace(/[_-]/g," ")}))},b.readAsDataURL(a),s(null)},C=a=>{a.preventDefault(),a.stopPropagation(),u("dragenter"===a.type||"dragover"===a.type)};return c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(z,{}),(0,d.jsx)("div",{className:"fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-xl w-full max-w-lg shadow-2xl",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Upload Meme"}),(0,d.jsx)("button",{onClick:()=>h(!1),className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,d.jsx)(o.A,{size:20})})]}),(0,d.jsxs)("div",{className:"p-6",children:[l&&(0,d.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4 flex items-center gap-2",children:[(0,d.jsx)(p.A,{size:16}),(0,d.jsx)("span",{className:"text-sm",children:l})]}),(0,d.jsxs)("form",{onSubmit:A,className:"space-y-4",children:[(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-all ${t?"border-blue-400 bg-blue-50":x.image?"border-green-400 bg-green-50":"border-gray-300 hover:border-gray-400"}`,onDragEnter:C,onDragLeave:C,onDragOver:C,onDrop:a=>{a.preventDefault(),a.stopPropagation(),u(!1);let b=a.dataTransfer.files?.[0];b&&B(b)},children:[(0,d.jsx)("input",{type:"file",accept:"image/*",onChange:a=>{let b=a.target.files?.[0];b&&B(b)},className:"hidden",id:"file-upload"}),x.preview?(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("div",{className:"relative w-32 h-32 mx-auto",children:(0,d.jsx)(f.default,{src:x.preview,alt:"Preview",fill:!0,className:"rounded-lg object-cover"})}),(0,d.jsx)("p",{className:"text-sm text-green-700 font-medium",children:x.image?.name}),(0,d.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,d.jsx)("label",{htmlFor:"file-upload",className:"px-4 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:"Change"}),(0,d.jsx)("button",{type:"button",onClick:()=>{y({title:"",tags:"",image:null,preview:null}),s(null)},className:"px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:"Remove"})]})]}):(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(q.A,{size:40,className:"mx-auto text-gray-400"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-lg font-medium text-gray-700 mb-1",children:"Drop your meme here"}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-2",children:"or click to browse"}),(0,d.jsxs)("div",{className:"flex items-center justify-center gap-4 text-xs text-gray-400",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(q.A,{size:12}),(0,d.jsx)("span",{children:"Drag & Drop"})]}),(0,d.jsx)("div",{className:"w-px h-4 bg-gray-300"}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(m,{size:12}),(0,d.jsx)("span",{children:"Ctrl+V to Paste"})]})]})]}),(0,d.jsx)("label",{htmlFor:"file-upload",className:"inline-block px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer transition-colors font-medium",children:"Choose File"}),(0,d.jsx)("p",{className:"text-xs text-gray-400",children:"PNG, JPG, GIF up to 10MB"})]})]})}),x.image&&(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tags (optional)"}),(0,d.jsx)("input",{type:"text",value:x.tags,onChange:a=>y(b=>({...b,tags:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors",placeholder:"funny, relatable, viral..."}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Separate with commas. Leave empty for auto-generated tags."})]})}),x.image&&(0,d.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,d.jsx)("button",{type:"button",onClick:()=>h(!1),className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",disabled:i,children:"Cancel"}),(0,d.jsx)("button",{type:"submit",disabled:i,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium flex items-center justify-center gap-2",children:i?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(r,{size:16,className:"animate-spin"}),"Uploading..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(q.A,{size:16}),"Upload Meme"]})})]})]})]})]})})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(z,{}),(0,d.jsx)("button",{onClick:()=>h(!0),className:"fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 group",title:"Upload a meme (or paste/drop anywhere)",children:(0,d.jsx)(n,{size:24,className:"group-hover:rotate-90 transition-transform duration-200"})})]})}},2846:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,624,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},3528:()=>{},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g,metadata:()=>e,viewport:()=>f});var d=c(7413);c(1135);let e={title:"Meme Database - Frontend",description:"A modern frontend for managing and browsing memes",manifest:"/manifest.json",appleWebApp:{capable:!0,statusBarStyle:"default",title:"MemeDB"},icons:{icon:[{url:"/icon.svg",type:"image/svg+xml"}],apple:[{url:"/icon.svg",type:"image/svg+xml"}]}},f={width:"device-width",initialScale:1,viewportFit:"cover",themeColor:"#2563eb"};function g({children:a}){return(0,d.jsxs)("html",{lang:"en",children:[(0,d.jsx)("head",{children:(0,d.jsx)("link",{rel:"manifest",href:"/manifest.json"})}),(0,d.jsxs)("body",{className:"font-sans antialiased",children:[a,(0,d.jsx)("script",{dangerouslySetInnerHTML:{__html:`
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('SW registered: ', registration);
                    
                    // Handle service worker updates
                    registration.addEventListener('updatefound', () => {
                      const newWorker = registration.installing;
                      if (newWorker) {
                        newWorker.addEventListener('statechange', () => {
                          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New service worker is available, refresh the page
                            console.log('New service worker available, refreshing...');
                            window.location.reload();
                          }
                        });
                      }
                    });
                    
                    // Check for updates every 60 seconds
                    setInterval(() => {
                      registration.update();
                    }, 60000);
                    
                  }, function(registrationError) {
                    console.log('SW registration failed: ', registrationError);
                  });
              });
              
              // Listen for service worker controller changes
              navigator.serviceWorker.addEventListener('controllerchange', () => {
                console.log('Service worker controller changed, reloading...');
                window.location.reload();
              });
            }
          `}})]})]})}},6023:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(2688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},7574:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},9976:()=>{}};