"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/MemeCard.tsx":
/*!*************************************!*\
  !*** ./src/components/MemeCard.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemeCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _FullScreenMeme__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FullScreenMeme */ \"(app-pages-browser)/./src/components/FullScreenMeme.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MemeCard(param) {\n    let { meme, onDelete } = param;\n    _s();\n    const [isFullScreenOpen, setIsFullScreenOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleDelete = ()=>{\n        if (onDelete) {\n            onDelete(meme.id);\n        }\n    };\n    const handleImageClick = ()=>{\n        setIsFullScreenOpen(true);\n    };\n    const handleCloseFullScreen = ()=>{\n        setIsFullScreenOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"group relative bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden border border-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative aspect-square overflow-hidden cursor-pointer bg-gray-50\",\n                        onClick: handleImageClick,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.getImageUrl)(meme.image_url),\n                                alt: meme.title || 'Meme',\n                                fill: true,\n                                className: \"object-cover group-hover:scale-[1.02] transition-transform duration-200\",\n                                sizes: \"(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw\",\n                                onError: (e)=>{\n                                    e.currentTarget.src = '/placeholder-meme.svg';\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleImageClick,\n                                    className: \"bg-white/95 hover:bg-white text-gray-700 hover:text-gray-900 p-3 rounded-full shadow-lg transition-all duration-200 hover:scale-110\",\n                                    title: \"View full size\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    handleDelete();\n                                },\n                                className: \"absolute top-2 right-2 bg-red-500/90 hover:bg-red-600 text-white p-1.5 rounded-full transition-all duration-200 opacity-0 group-hover:opacity-100 shadow-md hover:scale-110\",\n                                title: \"Delete meme\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 14\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    (meme.title || meme.tags.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3\",\n                        children: [\n                            meme.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-900 mb-2 line-clamp-2\",\n                                children: meme.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this),\n                            (()=>{\n                                const systemTags = [\n                                    'bookmarklet',\n                                    'drag-saved',\n                                    'browser-extension',\n                                    'saved'\n                                ];\n                                const visibleTags = meme.tags.filter((tag)=>!systemTags.includes(tag.toLowerCase()));\n                                return visibleTags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1 mb-2\",\n                                    children: [\n                                        visibleTags.slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block px-2 py-0.5 bg-blue-50 text-blue-600 text-xs rounded-md font-medium\",\n                                                children: [\n                                                    \"#\",\n                                                    tag\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 21\n                                            }, this)),\n                                        visibleTags.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-block px-2 py-0.5 bg-gray-50 text-gray-500 text-xs rounded-md\",\n                                            children: [\n                                                \"+\",\n                                                visibleTags.length - 2\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 17\n                                }, this);\n                            })(),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400\",\n                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.formatDate)(meme.created_at)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FullScreenMeme__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                meme: meme,\n                isOpen: isFullScreenOpen,\n                onClose: handleCloseFullScreen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\memedb\\\\meme-f\\\\src\\\\components\\\\MemeCard.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MemeCard, \"m20DnvRLgeSFQdR2KP4cA6MQQKo=\");\n_c = MemeCard;\nvar _c;\n$RefreshReg$(_c, \"MemeCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MemeCard.tsx\n"));

/***/ })

});