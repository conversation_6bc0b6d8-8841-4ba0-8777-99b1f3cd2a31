'use client';

import { useState } from 'react';
import { RotateCcw } from 'lucide-react';

export default function CacheClearButton() {
  const [clearing, setClearing] = useState(false);

  const clearAllCaches = async () => {
    if (!('caches' in window) || !('serviceWorker' in navigator)) {
      // Fallback: just reload the page
      window.location.reload();
      return;
    }

    setClearing(true);

    try {
      // Clear all caches
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );

      // Unregister service worker
      const registrations = await navigator.serviceWorker.getRegistrations();
      await Promise.all(
        registrations.map(registration => registration.unregister())
      );

      // Clear browser storage
      if ('localStorage' in window) {
        localStorage.clear();
      }
      if ('sessionStorage' in window) {
        sessionStorage.clear();
      }

      // Force reload
      window.location.reload();
    } catch (error) {
      console.error('Error clearing caches:', error);
      // Fallback: just reload
      window.location.reload();
    }
  };

  return (
    <button
      onClick={clearAllCaches}
      disabled={clearing}
      className="flex items-center gap-2 px-3 py-1.5 text-xs bg-orange-50 text-orange-700 hover:bg-orange-100 rounded-lg transition-colors font-medium disabled:opacity-50"
      title="Clear all caches and reload"
    >
      <RotateCcw size={14} className={clearing ? 'animate-spin' : ''} />
      {clearing ? 'Clearing...' : 'Clear Cache'}
    </button>
  );
}
