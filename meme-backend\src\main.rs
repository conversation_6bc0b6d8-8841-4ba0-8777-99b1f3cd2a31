use axum::{
    extract::{Multipart, Path, Query},
    http::StatusCode,
    response::Json,
    routing::{delete, get, post},
    Router,
};
use tower_http::services::ServeDir;
use tower_http::trace::TraceLayer;

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::{Row, SqlitePool};
use std::fs;
use std::io::Write;
use tower_http::cors::CorsLayer;
use uuid::Uuid;

use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Meme {
    pub id: Uuid,
    pub image_url: String,
    pub tags: Vec<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize)]
pub struct CreateMemeRequest {
    pub image_url: String,
    pub tags: Vec<String>,
}

#[derive(Debug, Deserialize)]
pub struct UploadUrlRequest {
    pub image_url: String,
    pub tags: Vec<String>,
    pub source_url: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct SearchQuery {
    pub q: Option<String>,     // Combined search query for tags
    pub tag: Option<String>,   // Keep for backward compatibility
    pub limit: Option<usize>,
}

#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: String,
}

type MemeStorage = SqlitePool;

async fn init_database() -> Result<SqlitePool, sqlx::Error> {
    // Ensure the data directory exists
    std::fs::create_dir_all("data").unwrap_or_else(|_| {});

    let pool = SqlitePool::connect("sqlite:data/memes.db").await?;

    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS memes (
            id TEXT PRIMARY KEY,
            image_url TEXT NOT NULL,
            tags TEXT NOT NULL,
            created_at TEXT NOT NULL
        )
        "#,
    )
    .execute(&pool)
    .await?;

    // Migration: Remove title column if it exists (for existing databases)
    // SQLite doesn't support DROP COLUMN directly, so we need to check if title column exists
    let table_info = sqlx::query("PRAGMA table_info(memes)")
        .fetch_all(&pool)
        .await?;

    let has_title_column = table_info.iter().any(|row| {
        let column_name: String = row.get("name");
        column_name == "title"
    });

    if has_title_column {
        // Create new table without title column
        sqlx::query(
            r#"
            CREATE TABLE memes_new (
                id TEXT PRIMARY KEY,
                image_url TEXT NOT NULL,
                tags TEXT NOT NULL,
                created_at TEXT NOT NULL
            )
            "#,
        )
        .execute(&pool)
        .await?;

        // Copy data from old table to new table (excluding title)
        sqlx::query(
            "INSERT INTO memes_new (id, image_url, tags, created_at) SELECT id, image_url, tags, created_at FROM memes"
        )
        .execute(&pool)
        .await?;

        // Drop old table and rename new table
        sqlx::query("DROP TABLE memes")
            .execute(&pool)
            .await?;

        sqlx::query("ALTER TABLE memes_new RENAME TO memes")
            .execute(&pool)
            .await?;
    }

    Ok(pool)
}

#[tokio::main]
async fn main() {
    // Initialize tracing subscriber for logging
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| {
                "meme_backend=debug,tower_http=debug,axum::routing=trace,sqlx=warn".into()
            }),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    let storage = init_database()
        .await
        .expect("Failed to initialize database");

    let app = Router::new()
        .route("/", get(health_check))
        .route("/api/memes", get(get_memes))
        .route("/api/memes", post(create_meme))
        .route("/api/memes/upload", post(upload_meme))
        .route("/api/memes/upload-url", post(upload_meme_from_url))
        .route("/api/memes/:id", get(get_meme_by_id))
        .route("/api/memes/:id", delete(delete_meme))
        .route("/api/memes/search", get(search_memes))
        .route("/api/tags", get(get_all_tags))
        .nest_service("/uploads", ServeDir::new("uploads"))
        .layer(TraceLayer::new_for_http())
        .with_state(storage)
        .layer(
            CorsLayer::new()
                .allow_origin(tower_http::cors::Any)
                .allow_methods(tower_http::cors::Any)
                .allow_headers(tower_http::cors::Any),
        );

    let listener = tokio::net::TcpListener::bind("127.0.0.1:3001")
        .await
        .unwrap();
    println!("🚀 Meme Backend running on http://127.0.0.1:3001");
    axum::serve(listener, app).await.unwrap();
}

async fn health_check() -> Json<ApiResponse<String>> {
    Json(ApiResponse {
        success: true,
        data: Some("Meme Backend is running!".to_string()),
        message: "Ok".to_string(),
    })
}

async fn create_meme(
    axum::extract::State(storage): axum::extract::State<MemeStorage>,
    Json(payload): Json<CreateMemeRequest>,
) -> Result<Json<ApiResponse<Meme>>, StatusCode> {
    let new_meme = Meme {
        id: Uuid::new_v4(),
        image_url: payload.image_url,
        tags: payload.tags,
        created_at: Utc::now(),
    };

    let tags_json = serde_json::to_string(&new_meme.tags).unwrap();

    let result = sqlx::query(
        "INSERT INTO memes (id, image_url, tags, created_at) VALUES (?, ?, ?, ?)",
    )
    .bind(new_meme.id.to_string())
    .bind(&new_meme.image_url)
    .bind(&tags_json)
    .bind(new_meme.created_at.to_rfc3339())
    .execute(&storage)
    .await;

    match result {
        Ok(_) => Ok(Json(ApiResponse {
            success: true,
            data: Some(new_meme),
            message: "Meme created successfully".to_string(),
        })),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn get_memes(
    axum::extract::State(storage): axum::extract::State<MemeStorage>,
) -> Result<Json<ApiResponse<Vec<Meme>>>, StatusCode> {
    let rows = sqlx::query("SELECT * FROM memes ORDER BY created_at DESC")
        .fetch_all(&storage)
        .await;

    match rows {
        Ok(rows) => {
            let mut memes = Vec::new();
            for row in rows {
                let id_str: String = row.get("id");
                let image_url: String = row.get("image_url");
                let tags_json: String = row.get("tags");
                let created_at_str: String = row.get("created_at");

                let id = Uuid::parse_str(&id_str).unwrap();
                let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap();
                let created_at = DateTime::parse_from_rfc3339(&created_at_str)
                    .unwrap()
                    .with_timezone(&Utc);

                memes.push(Meme {
                    id,
                    image_url,
                    tags,
                    created_at,
                });
            }
            Ok(Json(ApiResponse {
                success: true,
                data: Some(memes),
                message: "Memes retrieved successfully".to_string(),
            }))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn get_meme_by_id(
    axum::extract::State(storage): axum::extract::State<MemeStorage>,
    Path(id): Path<Uuid>,
) -> Result<Json<ApiResponse<Meme>>, StatusCode> {
    let row = sqlx::query("SELECT * FROM memes WHERE id = ?")
        .bind(id.to_string())
        .fetch_optional(&storage)
        .await;

    match row {
        Ok(Some(row)) => {
            let id_str: String = row.get("id");
            let image_url: String = row.get("image_url");
            let tags_json: String = row.get("tags");
            let created_at_str: String = row.get("created_at");

            let id = Uuid::parse_str(&id_str).unwrap();
            let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap();
            let created_at = DateTime::parse_from_rfc3339(&created_at_str)
                .unwrap()
                .with_timezone(&Utc);

            let meme = Meme {
                id,
                image_url,
                tags,
                created_at,
            };

            Ok(Json(ApiResponse {
                success: true,
                data: Some(meme),
                message: "Meme found".to_string(),
            }))
        }
        Ok(None) => Err(StatusCode::NOT_FOUND),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn delete_meme(
    axum::extract::State(storage): axum::extract::State<MemeStorage>,
    Path(id): Path<Uuid>,
) -> Result<Json<ApiResponse<String>>, StatusCode> {
    let result = sqlx::query("DELETE FROM memes WHERE id = ?")
        .bind(id.to_string())
        .execute(&storage)
        .await;

    match result {
        Ok(query_result) => {
            if query_result.rows_affected() > 0 {
                Ok(Json(ApiResponse {
                    success: true,
                    data: Some("Meme deleted".to_string()),
                    message: "Meme deleted successfully".to_string(),
                }))
            } else {
                Err(StatusCode::NOT_FOUND)
            }
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn search_memes(
    axum::extract::State(storage): axum::extract::State<MemeStorage>,
    Query(params): Query<SearchQuery>,
) -> Result<Json<ApiResponse<Vec<Meme>>>, StatusCode> {
    let mut query = "SELECT * FROM memes WHERE 1=1".to_string();
    let mut bind_values: Vec<String> = Vec::new();

    // Combined search query - enhanced parsing for tags and text
    if let Some(q) = &params.q {
        let search_text = q.to_lowercase();

        // Extract hashtags and regular text
        let mut tags: Vec<String> = Vec::new();
        let mut regular_text: Vec<String> = Vec::new();

        for word in search_text.split_whitespace() {
            if word.starts_with('#') {
                // Extract tag without the #
                let tag = word.trim_start_matches('#');
                if !tag.is_empty() {
                    tags.push(tag.to_string());
                }
            } else {
                regular_text.push(word.to_string());
            }
        }

        let mut conditions: Vec<String> = Vec::new();

        // Search for regular text in tags only
        if !regular_text.is_empty() {
            let text_search = regular_text.join(" ");
            conditions.push("tags LIKE ?".to_string());
            let search_term = format!("%{}%", text_search);
            bind_values.push(search_term);
        }

        // Search for specific tags
        for tag in tags {
            conditions.push("tags LIKE ?".to_string());
            bind_values.push(format!("%{}%", tag));
        }

        if !conditions.is_empty() {
            query.push_str(&format!(" AND ({})", conditions.join(" AND ")));
        }
    }

    // Individual tag filter (for backward compatibility)
    if let Some(tag) = &params.tag {
        query.push_str(" AND tags LIKE ?");
        bind_values.push(format!("%{}%", tag.to_lowercase()));
    }

    query.push_str(" ORDER BY created_at DESC");

    // Add limit if provided
    if let Some(limit) = params.limit {
        query.push_str(" LIMIT ?");
        bind_values.push(limit.to_string());
    }

    let mut sqlx_query = sqlx::query(&query);
    for value in &bind_values {
        sqlx_query = sqlx_query.bind(value);
    }

    let rows = sqlx_query.fetch_all(&storage).await;

    match rows {
        Ok(rows) => {
            let mut results = Vec::new();
            for row in rows {
                let id_str: String = row.get("id");
                let image_url: String = row.get("image_url");
                let tags_json: String = row.get("tags");
                let created_at_str: String = row.get("created_at");

                let id = Uuid::parse_str(&id_str).unwrap();
                let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap();
                let created_at = DateTime::parse_from_rfc3339(&created_at_str)
                    .unwrap()
                    .with_timezone(&Utc);

                results.push(Meme {
                    id,
                    image_url,
                    tags,
                    created_at,
                });
            }
            Ok(Json(ApiResponse {
                success: true,
                data: Some(results),
                message: "search completed".to_string(),
            }))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn get_all_tags(
    axum::extract::State(storage): axum::extract::State<MemeStorage>,
) -> Result<Json<ApiResponse<Vec<String>>>, StatusCode> {
    let rows = sqlx::query("SELECT tags FROM memes")
        .fetch_all(&storage)
        .await;

    match rows {
        Ok(rows) => {
            let mut all_tags: Vec<String> = Vec::new();

            for row in rows {
                let tags_json: String = row.get("tags");
                let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap();
                all_tags.extend(tags);
            }

            all_tags.sort();
            all_tags.dedup();

            Ok(Json(ApiResponse {
                success: true,
                data: Some(all_tags),
                message: "Tags retrieved successfully".to_string(),
            }))
        }
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn upload_meme(
    axum::extract::State(storage): axum::extract::State<MemeStorage>,
    mut multipart: Multipart,
) -> Result<Json<ApiResponse<Meme>>, StatusCode> {
    let mut tags: Vec<String> = Vec::new();
    let mut image_url: Option<String> = None;

    fs::create_dir_all("uploads").unwrap();

    while let Some(field) = multipart.next_field().await.unwrap() {
        let name = field.name().unwrap().to_string();

        match name.as_str() {
            "tags" => {
                let tags_str = field.text().await.unwrap();
                tags = tags_str.split(",").map(|s| s.trim().to_string()).collect();
            }
            "image" => {
                let data = field.bytes().await.unwrap();
                let filename = format!("{}.jpg", Uuid::new_v4());
                let filepath = format!("uploads/{}", filename);

                let mut file = std::fs::File::create(&filepath).unwrap();
                file.write_all(&data).unwrap();

                image_url = Some(format!("/uploads/{}", filename));
            }
            _ => {}
        }
    }

    if image_url.is_none() {
        return Err(StatusCode::BAD_REQUEST);
    }

    let new_meme = Meme {
        id: Uuid::new_v4(),
        image_url: image_url.unwrap(),
        tags,
        created_at: Utc::now(),
    };

    let tags_json = serde_json::to_string(&new_meme.tags).unwrap();

    let result = sqlx::query(
        "INSERT INTO memes (id, image_url, tags, created_at) VALUES (?, ?, ?, ?)",
    )
    .bind(new_meme.id.to_string())
    .bind(&new_meme.image_url)
    .bind(&tags_json)
    .bind(new_meme.created_at.to_rfc3339())
    .execute(&storage)
    .await;

    match result {
        Ok(_) => Ok(Json(ApiResponse {
            success: true,
            data: Some(new_meme),
            message: "Meme created successfully".to_string(),
        })),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

async fn upload_meme_from_url(
    axum::extract::State(storage): axum::extract::State<MemeStorage>,
    Json(payload): Json<UploadUrlRequest>,
) -> Result<Json<ApiResponse<Meme>>, StatusCode> {
    // Download the image from the URL with proper headers to avoid blocking
    let client = reqwest::Client::builder()
        .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        .timeout(std::time::Duration::from_secs(30))
        .build()
        .unwrap();

    let response = match client
        .get(&payload.image_url)
        .header("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
        .header("Accept-Language", "en-US,en;q=0.9")
        .header("Cache-Control", "no-cache")
        .header("Pragma", "no-cache")
        .header("Sec-Fetch-Dest", "image")
        .header("Sec-Fetch-Mode", "no-cors")
        .header("Sec-Fetch-Site", "cross-site")
        .send()
        .await
    {
        Ok(resp) => resp,
        Err(e) => {
            println!("Failed to fetch image from {}: {}", payload.image_url, e);
            return Err(StatusCode::BAD_REQUEST);
        }
    };

    if !response.status().is_success() {
        return Err(StatusCode::BAD_REQUEST);
    }

    let image_bytes = match response.bytes().await {
        Ok(bytes) => bytes.to_vec(),
        Err(_) => return Err(StatusCode::BAD_REQUEST),
    };

    // Create uploads directory if it doesn't exist
    fs::create_dir_all("uploads").unwrap();

    // Generate filename and save the image
    let filename = format!("{}.jpg", Uuid::new_v4());
    let filepath = format!("uploads/{}", filename);

    let mut file = match std::fs::File::create(&filepath) {
        Ok(file) => file,
        Err(_) => return Err(StatusCode::INTERNAL_SERVER_ERROR),
    };

    if file.write_all(&image_bytes).is_err() {
        return Err(StatusCode::INTERNAL_SERVER_ERROR);
    }

    // Create the meme record
    let new_meme = Meme {
        id: Uuid::new_v4(),
        image_url: format!("/uploads/{}", filename),
        tags: payload.tags,
        created_at: Utc::now(),
    };

    let tags_json = serde_json::to_string(&new_meme.tags).unwrap();

    let result = sqlx::query(
        "INSERT INTO memes (id, image_url, tags, created_at) VALUES (?, ?, ?, ?)",
    )
    .bind(new_meme.id.to_string())
    .bind(&new_meme.image_url)
    .bind(&tags_json)
    .bind(new_meme.created_at.to_rfc3339())
    .execute(&storage)
    .await;

    match result {
        Ok(_) => Ok(Json(ApiResponse {
            success: true,
            data: Some(new_meme),
            message: "Meme created successfully".to_string(),
        })),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}
