// Background script for MemeDB browser extension

// Create context menu when extension is installed
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: "save-to-memedb",
    title: "Save to MemeDB",
    contexts: ["image"]
  });
  
  chrome.contextMenus.create({
    id: "save-link-to-memedb", 
    title: "Save image to MemeDB",
    contexts: ["link"],
    targetUrlPatterns: ["*://*/*.jpg", "*://*/*.jpeg", "*://*/*.png", "*://*/*.gif", "*://*/*.webp"]
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "save-to-memedb") {
    saveImageToMemeDB(info.srcUrl, tab);
  } else if (info.menuItemId === "save-link-to-memedb") {
    saveImageToMemeDB(info.linkUrl, tab);
  }
});

// Function to save image to MemeDB
async function saveImageToMemeDB(imageUrl, tab) {
  try {
    // Get MemeDB API URL from storage or use default
    const result = await chrome.storage.sync.get(['memedbApiUrl']);
    const apiUrl = result.memedbApiUrl || 'http://127.0.0.1:3001';
    
    // Show loading notification
    showNotification('Saving to MemeDB...', 'loading');
    
    // Send image URL to MemeDB API
    const response = await fetch(`${apiUrl}/api/memes/upload-url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image_url: imageUrl,
        tags: ['browser-extension', 'saved', getDomainFromUrl(tab.url)],
        source_url: tab.url
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      showNotification('✅ Saved to MemeDB!', 'success');

      // Send success message to content script
      chrome.tabs.sendMessage(tab.id, {
        action: 'saveImageResult',
        success: true,
        message: '✅ Meme saved successfully!'
      });

      // Optionally open MemeDB in new tab
      const settings = await chrome.storage.sync.get(['openAfterSave', 'memedbUrl']);
      if (settings.openAfterSave) {
        const memedbUrl = settings.memedbUrl || 'http://localhost:3000';
        chrome.tabs.create({ url: memedbUrl });
      }
    } else {
      throw new Error(result.message || 'Failed to save');
    }
    
  } catch (error) {
    console.error('Error saving to MemeDB:', error);
    showNotification('❌ Failed to save: ' + error.message, 'error');

    // Send error message to content script
    chrome.tabs.sendMessage(tab.id, {
      action: 'saveImageResult',
      success: false,
      message: '❌ Failed to save: ' + error.message
    });
  }
}

// Show notification to user
function showNotification(message, type) {
  chrome.notifications.create({
    type: 'basic',
    iconUrl: 'icons/icon48.png',
    title: 'MemeDB Saver',
    message: message
  });
}

// Extract domain from URL
function getDomainFromUrl(url) {
  try {
    return new URL(url).hostname;
  } catch {
    return 'unknown';
  }
}

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'saveImage') {
    saveImageToMemeDB(request.imageUrl, {
      url: request.sourceUrl,
      title: request.title,
      id: sender.tab.id
    });
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // Inject content script to show floating save button
  chrome.scripting.executeScript({
    target: { tabId: tab.id },
    function: injectFloatingSaveButton
  });
});

// Function to inject floating save button (similar to bookmarklet)
function injectFloatingSaveButton() {
  // Check if button already exists
  if (document.getElementById('memedb-floating-button')) {
    return;
  }
  
  // Create floating button
  const button = document.createElement('div');
  button.id = 'memedb-floating-button';
  button.innerHTML = '🎭 MemeDB';
  button.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    font-weight: bold;
    z-index: 999999;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
    user-select: none;
  `;
  
  // Add hover effect
  button.addEventListener('mouseenter', () => {
    button.style.transform = 'scale(1.05)';
  });
  
  button.addEventListener('mouseleave', () => {
    button.style.transform = 'scale(1)';
  });
  
  // Add click handler to remove button
  button.addEventListener('click', () => {
    document.body.removeChild(button);
  });
  
  // Add to page
  document.body.appendChild(button);
  
  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (document.body.contains(button)) {
      document.body.removeChild(button);
    }
  }, 10000);
}
