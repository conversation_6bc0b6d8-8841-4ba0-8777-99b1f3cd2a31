// True Drag-to-Bookmark Meme Saver
// This creates a floating bookmark that users can drag images onto
(function() {
  // Configuration
  const API_URL = 'http://127.0.0.1:3001';
  const MEME_DB_URL = 'http://localhost:3000';
  
  // Check if bookmark already exists
  if (document.getElementById('memedb-floating-bookmark')) {
    const existing = document.getElementById('memedb-floating-bookmark');
    existing.style.animation = 'bounce 0.5s ease-in-out';
    setTimeout(() => existing.style.animation = '', 500);
    return;
  }

  // Create floating bookmark
  const bookmark = document.createElement('div');
  bookmark.id = 'memedb-floating-bookmark';
  bookmark.innerHTML = '🎭 MemeDB';
  bookmark.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    font-size: 14px;
    font-weight: bold;
    z-index: 999999;
    cursor: move;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    border: 3px solid transparent;
    transition: all 0.3s ease;
    user-select: none;
    text-align: center;
    min-width: 100px;
  `;

  // Add CSS animations
  const style = document.createElement('style');
  style.textContent = `
    @keyframes bounce {
      0%, 20%, 60%, 100% { transform: translateY(0); }
      40% { transform: translateY(-10px); }
      80% { transform: translateY(-5px); }
    }
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
      100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
    }
    @keyframes glow {
      0%, 100% { border-color: #667eea; }
      50% { border-color: #764ba2; }
    }
  `;
  document.head.appendChild(style);

  // Make bookmark draggable
  let isDragging = false;
  let dragOffset = { x: 0, y: 0 };

  bookmark.addEventListener('mousedown', (e) => {
    isDragging = true;
    dragOffset.x = e.clientX - bookmark.offsetLeft;
    dragOffset.y = e.clientY - bookmark.offsetTop;
    bookmark.style.cursor = 'grabbing';
    e.preventDefault();
  });

  document.addEventListener('mousemove', (e) => {
    if (isDragging) {
      bookmark.style.left = (e.clientX - dragOffset.x) + 'px';
      bookmark.style.top = (e.clientY - dragOffset.y) + 'px';
      bookmark.style.right = 'auto';
    }
  });

  document.addEventListener('mouseup', () => {
    if (isDragging) {
      isDragging = false;
      bookmark.style.cursor = 'move';
    }
  });

  // Set up drag and drop functionality
  bookmark.addEventListener('dragover', (e) => {
    e.preventDefault();
    bookmark.style.border = '3px solid #10b981';
    bookmark.style.animation = 'glow 1s infinite';
    bookmark.innerHTML = '🎯 Drop Here!';
  });

  bookmark.addEventListener('dragleave', (e) => {
    e.preventDefault();
    bookmark.style.border = '3px solid transparent';
    bookmark.style.animation = '';
    bookmark.innerHTML = '🎭 MemeDB';
  });

  bookmark.addEventListener('drop', async (e) => {
    e.preventDefault();
    bookmark.style.border = '3px solid transparent';
    bookmark.style.animation = '';
    bookmark.innerHTML = '🎭 MemeDB';

    const imageUrl = e.dataTransfer.getData('text/html');
    const imageUrlMatch = imageUrl.match(/<img[^>]+src="([^">]+)"/);

    let finalImageUrl = null;

    if (imageUrlMatch) {
      finalImageUrl = imageUrlMatch[1];
    } else {
      // Try to get URL directly
      const urlData = e.dataTransfer.getData('text/uri-list') || e.dataTransfer.getData('text/plain');
      if (urlData && (urlData.includes('.jpg') || urlData.includes('.png') || urlData.includes('.gif') || urlData.includes('.webp'))) {
        finalImageUrl = urlData;
      }
    }

    if (finalImageUrl) {
      // Show loading and get auto-tags first
      bookmark.innerHTML = '🔍 Getting suggestions...';
      bookmark.style.background = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';

      try {
        const hostname = window.location.hostname.replace('www.', '');
        const title = `Meme saved from ${hostname || 'web'}`;
        const autoTags = await getAutoTags(finalImageUrl, title);

        // Reset bookmark appearance
        bookmark.innerHTML = '🎭 MemeDB';
        bookmark.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

        // Show the improved tag input modal
        const customTags = await createTagInputModal(autoTags, finalImageUrl);

        if (customTags !== null) { // User didn't cancel
          await saveMemeFromUrl(finalImageUrl, customTags);
        }
      } catch (error) {
        // Reset bookmark appearance
        bookmark.innerHTML = '🎭 MemeDB';
        bookmark.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';

        // Fallback to simple prompt
        const customTags = prompt('Add tags for this meme (optional):\nSeparate with commas, or leave empty for auto-tags', '');
        if (customTags !== null) {
          await saveMemeFromUrl(finalImageUrl, customTags);
        }
      }
    } else {
      showNotification('❌ Please drag an image!', '#ef4444');
    }
  });

  // Add global drag detection for better UX
  let dragCounter = 0;
  
  document.addEventListener('dragenter', (e) => {
    dragCounter++;
    if (e.dataTransfer.types.includes('text/html') || e.dataTransfer.types.includes('Files')) {
      bookmark.style.animation = 'pulse 1s infinite';
      bookmark.style.transform = 'scale(1.1)';
    }
  });

  document.addEventListener('dragleave', (e) => {
    dragCounter--;
    if (dragCounter === 0) {
      bookmark.style.animation = '';
      bookmark.style.transform = 'scale(1)';
    }
  });

  document.addEventListener('drop', (e) => {
    dragCounter = 0;
    bookmark.style.animation = '';
    bookmark.style.transform = 'scale(1)';
  });

  // Function to get auto-generated tags for an image
  async function getAutoTags(imageUrl, title) {
    try {
      // Convert image URL to base64 for analysis
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = async function() {
          try {
            const base64Data = reader.result.split(',')[1]; // Remove data:image/...;base64, prefix

            const analysisResponse = await fetch(`${MEME_DB_URL}/api/analyze-meme`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                image: base64Data,
                title: title,
                mimeType: blob.type
              })
            });

            if (analysisResponse.ok) {
              const result = await analysisResponse.json();
              resolve(result.tags || []);
            } else {
              resolve([]);
            }
          } catch (error) {
            console.warn('Auto-tagging failed:', error);
            resolve([]);
          }
        };
        reader.onerror = () => resolve([]);
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.warn('Failed to get auto tags:', error);
      return [];
    }
  }

  // Function to create a better tag input modal
  function createTagInputModal(autoTags, imageUrl) {
    return new Promise((resolve) => {
      // Create modal overlay
      const overlay = document.createElement('div');
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 10000000;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `;

      // Create modal content
      const modal = document.createElement('div');
      modal.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 24px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        color: #333;
      `;

      // Create modal HTML
      modal.innerHTML = `
        <div style="text-align: center; margin-bottom: 20px;">
          <h3 style="margin: 0 0 8px 0; color: #333; font-size: 18px;">🎭 Save to MemeDB</h3>
          <img src="${imageUrl}" style="max-width: 100%; max-height: 120px; border-radius: 8px; margin: 8px 0;" onerror="this.style.display='none'">
        </div>

        <div style="margin-bottom: 16px;">
          <label style="display: block; font-weight: 600; margin-bottom: 8px; color: #555;">
            🤖 Auto-generated tags:
          </label>
          <div id="auto-tags-container" style="margin-bottom: 12px;">
            ${autoTags.length > 0 ?
              autoTags.map(tag => `<span style="display: inline-block; background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 16px; margin: 2px; font-size: 12px;">${tag}</span>`).join('') :
              '<span style="color: #999; font-style: italic;">No auto-tags generated</span>'
            }
          </div>
        </div>

        <div style="margin-bottom: 20px;">
          <label style="display: block; font-weight: 600; margin-bottom: 8px; color: #555;">
            ✏️ Add custom tags (optional):
          </label>
          <input type="text" id="custom-tags-input" placeholder="funny, relatable, viral..."
                 style="width: 100%; padding: 8px 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 14px; box-sizing: border-box;">
          <div style="font-size: 12px; color: #666; margin-top: 4px;">Separate with commas</div>
        </div>

        <div style="display: flex; gap: 12px; justify-content: flex-end;">
          <button id="cancel-btn" style="padding: 8px 16px; border: 2px solid #ddd; background: white; color: #666; border-radius: 6px; cursor: pointer; font-size: 14px;">Cancel</button>
          <button id="save-btn" style="padding: 8px 16px; border: none; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 600;">Save Meme</button>
        </div>
      `;

      overlay.appendChild(modal);
      document.body.appendChild(overlay);

      // Focus on input
      setTimeout(() => {
        const input = document.getElementById('custom-tags-input');
        if (input) input.focus();
      }, 100);

      // Handle buttons
      document.getElementById('cancel-btn').onclick = () => {
        document.body.removeChild(overlay);
        resolve(null);
      };

      document.getElementById('save-btn').onclick = () => {
        const customInput = document.getElementById('custom-tags-input').value;
        document.body.removeChild(overlay);
        resolve(customInput);
      };

      // Handle Enter key
      document.getElementById('custom-tags-input').onkeypress = (e) => {
        if (e.key === 'Enter') {
          const customInput = document.getElementById('custom-tags-input').value;
          document.body.removeChild(overlay);
          resolve(customInput);
        }
      };

      // Handle Escape key
      document.onkeydown = (e) => {
        if (e.key === 'Escape') {
          document.body.removeChild(overlay);
          resolve(null);
        }
      };

      // Handle overlay click
      overlay.onclick = (e) => {
        if (e.target === overlay) {
          document.body.removeChild(overlay);
          resolve(null);
        }
      };
    });
  }

  // Function to save meme from URL
  async function saveMemeFromUrl(imageUrl, customTags = null) {
    try {
      // Show loading state
      const originalContent = bookmark.innerHTML;
      bookmark.innerHTML = '⏳ Analyzing...';
      bookmark.style.background = 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)';

      showNotification('🔍 Analyzing image for auto-tags...', '#3b82f6');

      // Get auto-generated tags
      const hostname = window.location.hostname.replace('www.', '');
      const title = `Meme saved from ${hostname || 'web'}`;
      const autoTags = await getAutoTags(imageUrl, title);

      // Prepare tags - start with system tags (not shown to user)
      let systemTags = ['bookmarklet', 'drag-saved'];
      let userVisibleTags = [];

      // Add hostname as tag (clean it up) - this one is user-visible
      if (hostname && hostname !== 'localhost') {
        userVisibleTags.push(hostname);
      }

      // Add auto-generated tags
      if (autoTags.length > 0) {
        userVisibleTags = userVisibleTags.concat(autoTags);
      }

      // Add custom tags if provided
      if (customTags && customTags.trim()) {
        const userTags = customTags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
        userVisibleTags = userVisibleTags.concat(userTags);
      }

      // Combine system tags with user-visible tags for backend
      const allTags = systemTags.concat(userVisibleTags);

      // Update status
      bookmark.innerHTML = '⏳ Saving...';
      showNotification('📤 Downloading and saving meme...', '#3b82f6');

      const response = await fetch(`${API_URL}/api/memes/upload-url`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image_url: imageUrl,
          title: `Meme saved from ${hostname || 'web'}`,
          tags: allTags, // Send all tags to backend
          source_url: window.location.href
        })
      });

      const result = await response.json();

      if (result.success) {
        bookmark.innerHTML = '✅ Saved!';
        bookmark.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';

        // Only show user-visible tags in notification (no system tags)
        const tagsDisplay = userVisibleTags.length > 0 ? `<br><small>Tags: ${userVisibleTags.join(', ')}</small>` : '';
        showNotification(
          `🎉 Meme saved successfully!${tagsDisplay}<br><a href="${MEME_DB_URL}" target="_blank" style="color: white; text-decoration: underline;">View in MemeDB →</a>`,
          '#10b981'
        );

        setTimeout(() => {
          bookmark.innerHTML = originalContent;
          bookmark.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }, 2000);
      } else {
        throw new Error(result.message || 'Failed to save meme');
      }
    } catch (error) {
      console.error('Error saving meme:', error);
      bookmark.innerHTML = '❌ Error';
      bookmark.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
      
      showNotification(`❌ Failed to save: ${error.message}`, '#ef4444');
      
      setTimeout(() => {
        bookmark.innerHTML = '🎭 MemeDB';
        bookmark.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
      }, 3000);
    }
  }

  // Function to show notifications
  function showNotification(text, color = '#10b981') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: ${color};
      color: white;
      padding: 15px 25px;
      border-radius: 8px;
      z-index: 1000000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 20px rgba(0,0,0,0.2);
      max-width: 400px;
      text-align: center;
      animation: slideDown 0.3s ease-out;
    `;
    notification.innerHTML = text;
    
    // Add slide animation
    const notificationStyle = document.createElement('style');
    notificationStyle.textContent = `
      @keyframes slideDown {
        from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        to { transform: translateX(-50%) translateY(0); opacity: 1; }
      }
    `;
    document.head.appendChild(notificationStyle);
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (document.body.contains(notification)) {
        notification.style.animation = 'slideDown 0.3s ease-out reverse';
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 300);
      }
    }, 4000);
  }

  // Add close functionality
  bookmark.addEventListener('dblclick', () => {
    if (confirm('Remove MemeDB bookmark?')) {
      document.body.removeChild(bookmark);
    }
  });

  // Add to page
  document.body.appendChild(bookmark);
  
  // Show initial notification
  showNotification('🎭 MemeDB bookmark ready! Drag images onto it to save them.', '#667eea');
})();
