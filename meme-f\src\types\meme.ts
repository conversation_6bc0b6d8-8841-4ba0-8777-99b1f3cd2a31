// Types matching the Rust backend data structures

export interface Meme {
  id: string;
  image_url: string;
  tags: string[];
  created_at: string;
}

export interface CreateMemeRequest {
  image_url: string;
  tags: string[];
}

export interface SearchQuery {
  q?: string; // Combined search query for tags
  tag?: string; // Keep for backward compatibility
  limit?: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
}

export interface UploadMemeFormData {
  tags: string;
  image: File;
}
