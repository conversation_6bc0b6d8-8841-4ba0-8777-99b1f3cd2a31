'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { uploadMeme } from '@/lib/api';
import { UploadMemeFormData } from '@/types/meme';
import { analyzeImageForTags } from '@/lib/autoTag';
import { Upload, Plus, X, AlertCircle, Loader2, Clipboard } from 'lucide-react';

interface UploadMemeProps {
  onUploadSuccess?: () => void;
  initialData?: {
    text?: string;
    url?: string;
    imageUrl?: string;
    hasImage?: boolean;
  };
}

export default function UploadMeme({ onUploadSuccess, initialData }: UploadMemeProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [showPasteHint, setShowPasteHint] = useState(false);
  
  const [formData, setFormData] = useState({
    tags: '',
    image: null as File | null,
    preview: null as string | null,
  });

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData?.text && !formData.tags) {
      setFormData(prev => ({
        ...prev,
        tags: initialData.text ? `shared, ${initialData.text.slice(0, 20)}` : 'shared'
      }));
    }
  }, [initialData, formData.tags]);

  // Global paste handler
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      // Only handle paste if modal is open or we're not in an input field
      const activeElement = document.activeElement;
      const isInInput = activeElement instanceof HTMLInputElement || 
                       activeElement instanceof HTMLTextAreaElement ||
                       activeElement?.getAttribute('contenteditable') === 'true';
      
      if (!isOpen && !isInInput) {
        // Show paste hint for a few seconds
        const clipboardItems = e.clipboardData?.items;
        if (clipboardItems) {
          for (let i = 0; i < clipboardItems.length; i++) {
            if (clipboardItems[i].type.startsWith('image/')) {
              setShowPasteHint(true);
              setTimeout(() => setShowPasteHint(false), 3000);
              break;
            }
          }
        }
        return;
      }

      if (isOpen && !isInInput) {
        const clipboardItems = e.clipboardData?.items;
        if (clipboardItems) {
          for (let i = 0; i < clipboardItems.length; i++) {
            if (clipboardItems[i].type.startsWith('image/')) {
              e.preventDefault();
              const blob = clipboardItems[i].getAsFile();
              if (blob) {
                handleFileSelect(blob);
              }
              break;
            }
          }
        }
      }
    };

    const handleGlobalDrop = (e: DragEvent) => {
      // Prevent default browser behavior for file drops
      if (e.dataTransfer?.types.includes('Files')) {
        e.preventDefault();
        
        if (!isOpen) {
          // Auto-open modal if user drops a file anywhere
          const file = e.dataTransfer.files?.[0];
          if (file && file.type.startsWith('image/')) {
            setIsOpen(true);
            setTimeout(() => handleFileSelect(file), 100);
          }
        }
      }
    };

    const handleGlobalDragOver = (e: DragEvent) => {
      if (e.dataTransfer?.types.includes('Files')) {
        e.preventDefault();
      }
    };

    document.addEventListener('paste', handlePaste);
    document.addEventListener('drop', handleGlobalDrop);
    document.addEventListener('dragover', handleGlobalDragOver);

    return () => {
      document.removeEventListener('paste', handlePaste);
      document.removeEventListener('drop', handleGlobalDrop);
      document.removeEventListener('dragover', handleGlobalDragOver);
    };
  }, [isOpen]);

  // Show paste hint notification
  const PasteHint = () => (
    showPasteHint && (
      <div className="fixed top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-pulse">
        <div className="flex items-center gap-2">
          <Clipboard size={16} />
          <span className="text-sm">Image copied! Open upload to paste it</span>
        </div>
      </div>
    )
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!formData.image) {
      setError('Please select an image');
      return;
    }

    setLoading(true);

    try {
      // Auto-generate tags if none provided
      let finalTags = formData.tags.trim();
      if (!finalTags) {
        try {
          const result = await analyzeImageForTags(formData.image);
          finalTags = result.tags.join(', ') || 'funny, meme';
        } catch {
          finalTags = 'funny, meme';
        }
      }

      const uploadData: UploadMemeFormData = {
        tags: finalTags,
        image: formData.image,
      };

      const response = await uploadMeme(uploadData);

      if (response.success) {
        setFormData({ tags: '', image: null, preview: null });
        setIsOpen(false);
        onUploadSuccess?.();
      } else {
        setError(response.message || 'Upload failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      setError('File size must be less than 10MB');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setFormData(prev => ({
        ...prev,
        image: file,
        preview: e.target?.result as string
      }));
    };
    reader.readAsDataURL(file);
    setError(null);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) handleFileSelect(file);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(e.type === "dragenter" || e.type === "dragover");
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const file = e.dataTransfer.files?.[0];
    if (file) handleFileSelect(file);
  };

  const resetForm = () => {
    setFormData({ tags: '', image: null, preview: null });
    setError(null);
  };

  if (!isOpen) {
    return (
      <>
        <PasteHint />
        <button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 group"
          title="Upload a meme (or paste/drop anywhere)"
        >
          <Plus size={24} className="group-hover:rotate-90 transition-transform duration-200" />
        </button>
      </>
    );
  }

  return (
    <>
      <PasteHint />
      <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-xl w-full max-w-lg shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Upload Meme</h2>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4 flex items-center gap-2">
              <AlertCircle size={16} />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* File Upload Area */}
            <div>
              <div 
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-all ${
                  dragActive 
                    ? 'border-blue-400 bg-blue-50' 
                    : formData.image 
                      ? 'border-green-400 bg-green-50' 
                      : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                  id="file-upload"
                />

                {formData.preview ? (
                  <div className="space-y-3">
                    <div className="relative w-32 h-32 mx-auto">
                      <Image 
                        src={formData.preview} 
                        alt="Preview" 
                        fill
                        className="rounded-lg object-cover"
                      />
                    </div>
                    <p className="text-sm text-green-700 font-medium">{formData.image?.name}</p>
                    <div className="flex gap-2 justify-center">
                      <label
                        htmlFor="file-upload"
                        className="px-4 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      >
                        Change
                      </label>
                      <button
                        type="button"
                        onClick={resetForm}
                        className="px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      >
                        Remove
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <Upload size={40} className="mx-auto text-gray-400" />
                    <div>
                      <p className="text-lg font-medium text-gray-700 mb-1">
                        Drop your meme here
                      </p>
                      <p className="text-sm text-gray-500 mb-2">or click to browse</p>
                      <div className="flex items-center justify-center gap-4 text-xs text-gray-400">
                        <div className="flex items-center gap-1">
                          <Upload size={12} />
                          <span>Drag & Drop</span>
                        </div>
                        <div className="w-px h-4 bg-gray-300"></div>
                        <div className="flex items-center gap-1">
                          <Clipboard size={12} />
                          <span>Ctrl+V to Paste</span>
                        </div>
                      </div>
                    </div>
                    <label
                      htmlFor="file-upload"
                      className="inline-block px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer transition-colors font-medium"
                    >
                      Choose File
                    </label>
                    <p className="text-xs text-gray-400">PNG, JPG, GIF up to 10MB</p>
                  </div>
                )}
              </div>
            </div>

            {/* Form Fields - Only show when image is selected */}
            {formData.image && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags (optional)
                  </label>
                  <input
                    type="text"
                    value={formData.tags}
                    onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                    placeholder="funny, relatable, viral..."
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Separate with commas. Leave empty for auto-generated tags.
                  </p>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            {formData.image && (
              <div className="flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium flex items-center justify-center gap-2"
                >
                  {loading ? (
                    <>
                      <Loader2 size={16} className="animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload size={16} />
                      Upload Meme
                    </>
                  )}
                </button>
              </div>
            )}
          </form>
        </div>
        </div>
      </div>
    </>
  );
}