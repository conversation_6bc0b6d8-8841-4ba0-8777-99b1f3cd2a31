// AI-powered auto-tagging utility for memes
export interface AutoTagResult {
  tags: string[];
  confidence: number;
  category?: string;
  description?: string;
}

// Convert image file to base64 for API
async function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const base64 = reader.result as string;
      // Remove data:image/jpeg;base64, prefix
      resolve(base64.split(',')[1]);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

// AI-powered image analysis
async function analyzeImageWithAI(file: File): Promise<AutoTagResult> {
  try {
    const base64Image = await fileToBase64(file);

    const response = await fetch('/api/analyze-meme', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image: base64Image,
        mimeType: file.type
      }),
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }

    const result = await response.json();
    return {
      tags: result.tags || [],
      confidence: result.confidence || 0.9,
      category: result.category,
      description: result.description
    };
  } catch (error) {
    console.error('AI analysis failed:', error);
    throw error;
  }
}

// Common meme patterns and keywords
const MEME_PATTERNS = {
  // Text-based detection patterns
  textPatterns: [
    { pattern: /\b(lol|lmao|rofl|haha|funny)\b/i, tags: ['funny', 'humor'] },
    { pattern: /\b(cat|kitten|kitty|feline)\b/i, tags: ['cat', 'animal'] },
    { pattern: /\b(dog|puppy|doggo|pupper)\b/i, tags: ['dog', 'animal'] },
    { pattern: /\b(reaction|mood|feel|when)\b/i, tags: ['reaction'] },
    { pattern: /\b(fails?|epic fail|oops)\b/i, tags: ['fail', 'funny'] },
    { pattern: /\b(drake|pointing|choose)\b/i, tags: ['drake', 'choice'] },
    { pattern: /\b(stonks|money|profit)\b/i, tags: ['stonks', 'finance'] },
    { pattern: /\b(surprised|shocked|pikachu)\b/i, tags: ['surprised', 'reaction'] },
    { pattern: /\b(distracted|boyfriend|girlfriend)\b/i, tags: ['distracted-boyfriend'] },
    { pattern: /\b(this is fine|fire|burning)\b/i, tags: ['this-is-fine', 'stress'] },
    { pattern: /\b(brain|galaxy|expanding)\b/i, tags: ['expanding-brain', 'smart'] },
    { pattern: /\b(change my mind|prove me wrong)\b/i, tags: ['change-my-mind', 'debate'] },
    { pattern: /\b(wojak|doomer|bloomer|coomer)\b/i, tags: ['wojak'] },
    { pattern: /\b(pepe|frog|rare pepe)\b/i, tags: ['pepe', 'frog'] },
    { pattern: /\b(chad|virgin|vs)\b/i, tags: ['chad', 'virgin-vs-chad'] },
    { pattern: /\b(gaming|gamer|game)\b/i, tags: ['gaming'] },
    { pattern: /\b(programming|code|developer|bug)\b/i, tags: ['programming', 'tech'] },
    { pattern: /\b(work|office|boss|job)\b/i, tags: ['work', 'office'] },
    { pattern: /\b(school|student|teacher|exam)\b/i, tags: ['school', 'education'] },
    { pattern: /\b(weekend|monday|friday)\b/i, tags: ['weekend', 'relatable'] },
    { pattern: /\b(covid|pandemic|mask|vaccine)\b/i, tags: ['covid', 'pandemic'] },
    { pattern: /\b(2024|2025|new year)\b/i, tags: ['current-year'] },
  ],
  
  // File name patterns
  filenamePatterns: [
    { pattern: /funny/i, tags: ['funny'] },
    { pattern: /cat/i, tags: ['cat'] },
    { pattern: /dog/i, tags: ['dog'] },
    { pattern: /reaction/i, tags: ['reaction'] },
    { pattern: /drake/i, tags: ['drake'] },
    { pattern: /stonks/i, tags: ['stonks'] },
    { pattern: /pikachu/i, tags: ['pikachu', 'surprised'] },
    { pattern: /wojak/i, tags: ['wojak'] },
    { pattern: /pepe/i, tags: ['pepe'] },
    { pattern: /chad/i, tags: ['chad'] },
  ],

  // Common meme formats/templates
  templates: [
    'drake-pointing',
    'distracted-boyfriend',
    'expanding-brain',
    'change-my-mind',
    'this-is-fine',
    'surprised-pikachu',
    'stonks',
    'wojak',
    'pepe',
    'chad-vs-virgin',
    'galaxy-brain',
    'two-buttons',
    'epic-handshake',
    'woman-yelling-at-cat',
    'always-has-been',
  ]
};

// Popular tag suggestions based on current trends
const TRENDING_TAGS = [
  'relatable',
  'mood',
  'funny',
  'reaction',
  'wholesome',
  'cursed',
  'blessed',
  'cringe',
  'based',
  'sus',
  'meta',
  'deep-fried',
  'low-effort',
  'high-quality',
  'original',
  'template',
];

export async function analyzeImageForTags(file: File): Promise<AutoTagResult> {
  try {
    // Try AI analysis first
    return await analyzeImageWithAI(file);
  } catch (error) {
    console.warn('AI analysis failed, using fallback:', error);
    // Fall back to pattern matching
    return fallbackAnalysis(file);
  }
}

// Fallback analysis using pattern matching
function fallbackAnalysis(file: File): AutoTagResult {
  const tags = new Set<string>();
  let confidence = 0.3;

  // Analyze filename
  const filename = file.name.toLowerCase();
  MEME_PATTERNS.filenamePatterns.forEach(({ pattern, tags: patternTags }) => {
    if (pattern.test(filename)) {
      patternTags.forEach(tag => tags.add(tag));
      confidence += 0.2;
    }
  });

  // Add some trending tags based on current date/context
  const currentYear = new Date().getFullYear();
  if (filename.includes(currentYear.toString())) {
    tags.add('current-year');
    confidence += 0.1;
  }

  // Always ensure we have at least some basic tags
  if (tags.size === 0) {
    tags.add('funny');
    tags.add('relatable');
    confidence = 0.5;
  }

  // Limit to most relevant tags (max 3 for optimal tagging)
  const finalTags = Array.from(tags).slice(0, 3);

  return {
    tags: finalTags,
    confidence: Math.min(confidence, 1.0),
    category: 'pattern_matched'
  };
}

export function suggestAdditionalTags(existingTags: string[]): string[] {
  const suggestions = new Set<string>();
  const existingSet = new Set(existingTags.map(t => t.toLowerCase()));

  // Suggest related tags based on existing ones
  existingTags.forEach(tag => {
    const tagLower = tag.toLowerCase();
    
    // Category-based suggestions
    if (['cat', 'dog', 'animal'].some(t => tagLower.includes(t))) {
      ['cute', 'pet', 'wholesome'].forEach(s => {
        if (!existingSet.has(s)) suggestions.add(s);
      });
    }
    
    if (['funny', 'humor', 'lol'].some(t => tagLower.includes(t))) {
      ['comedy', 'hilarious', 'joke'].forEach(s => {
        if (!existingSet.has(s)) suggestions.add(s);
      });
    }
    
    if (['gaming', 'game'].some(t => tagLower.includes(t))) {
      ['gamer', 'videogame', 'esports'].forEach(s => {
        if (!existingSet.has(s)) suggestions.add(s);
      });
    }
    
    if (['work', 'office', 'job'].some(t => tagLower.includes(t))) {
      ['relatable', 'monday', 'stress'].forEach(s => {
        if (!existingSet.has(s)) suggestions.add(s);
      });
    }
  });

  // Add some trending suggestions if we don't have many
  if (suggestions.size < 2) {
    TRENDING_TAGS.forEach(tag => {
      if (!existingSet.has(tag) && suggestions.size < 3) {
        suggestions.add(tag);
      }
    });
  }

  return Array.from(suggestions).slice(0, 3);
}

// Quick tag suggestions for empty state
export function getQuickTagSuggestions(): string[] {
  return [
    'funny', 'relatable', 'mood', 'reaction', 'wholesome',
    'cat', 'dog', 'gaming', 'work', 'school'
  ];
}
